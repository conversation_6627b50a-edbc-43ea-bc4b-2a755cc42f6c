#!/usr/bin/env python3
"""
清理消息文件中的重复内容
"""

import os
import sys
import json
from pathlib import Path

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.system('chcp 65001 >nul 2>&1')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

def clean_duplicate_messages():
    """清理所有消息文件中的重复内容"""
    
    messages_dir = Path("messages")
    if not messages_dir.exists():
        print("消息目录不存在")
        return
    
    # 处理所有.jsonl文件
    for message_file in messages_dir.glob("*.jsonl"):
        print(f"处理文件: {message_file}")
        
        try:
            # 读取所有消息
            messages = []
            seen_messages = set()
            
            with open(message_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        message = json.loads(line)
                        
                        # 生成消息唯一标识
                        message_key = (
                            message.get('name', ''),
                            message.get('content', ''),
                            message.get('timestamp', ''),
                            str(message.get('raw_content', ''))
                        )
                        
                        # 检查是否重复
                        if message_key not in seen_messages:
                            seen_messages.add(message_key)
                            messages.append(message)
                            
                    except json.JSONDecodeError as e:
                        print(f"  跳过无效JSON (行 {line_num}): {e}")
                        continue
            
            # 如果有重复消息被删除，重写文件
            if len(messages) < line_num - 1:  # 减去空行
                print(f"  重写文件，从 {line_num-1} 条消息减少到 {len(messages)} 条")

                # 备份原文件
                backup_file = message_file.with_suffix('.jsonl.backup')
                message_file.rename(backup_file)
                print(f"  原文件备份为: {backup_file}")

                # 写入去重后的消息
                with open(message_file, 'w', encoding='utf-8') as f:
                    for message in messages:
                        f.write(json.dumps(message, ensure_ascii=False) + '\n')

                print(f"  ✅ 文件清理完成")
            else:
                print(f"  ✅ 文件无重复，无需处理")
                
        except Exception as e:
            print(f"  ❌ 处理文件时出错: {e}")
            continue
    
    print("\n所有文件处理完成")

if __name__ == "__main__":
    clean_duplicate_messages()
