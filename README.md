# Super Stock Agents (HTML + Flask版本)

一个基于多代理系统的股票题材分析平台，使用现代化的Web界面替代了原有的Gradio界面。

## 🌟 特性

- **现代化Web界面**: 使用HTML + CSS + JavaScript构建的响应式界面
- **实时通信**: 基于WebSocket的实时消息传输
- **多代理协作**: 5个专业代理协同工作
- **美观的UI设计**: 现代化的界面设计和动画效果
- **移动端适配**: 响应式设计，支持移动设备访问

## 👥 专家团队

- **👔 <PERSON> (CEO)**: 总体规划和任务分配
- **🔍 Leo (信息检索专家)**: 网络信息检索和筛选  
- **📊 Ken (题材挖掘专家)**: 题材逻辑和分支挖掘
- **💹 Lus (A股炒作大师)**: 个股挖掘和炒作分析
- **📝 Jess (秘书)**: 信息汇总和报告生成

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 现代浏览器 (Chrome, Firefox, Safari, Edge)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd stock_agent_html
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境变量**
   
   创建 `.env` 文件并设置以下变量：
   ```env
   API_URL=your_api_url_here
   API_KEY=your_api_key_here
   ```

4. **启动系统**
   
   **方式一**: 使用Python启动
   ```bash
   python run.py
   ```
   
   **方式二**: Windows用户可以双击 `start.bat`

5. **访问系统**
   
   打开浏览器访问: http://localhost:5000

## 📖 使用说明

1. **初始化系统**
   - 首次访问时，点击左侧的"初始化代理"按钮
   - 等待系统初始化完成

2. **开始咨询**
   - 在聊天界面输入您想分析的题材
   - 例如："请分析人工智能题材"
   - 系统将自动协调多个专家进行分析

3. **查看结果**
   - 各个专家会实时返回分析结果
   - 最终由秘书Jess汇总生成完整报告

## 🎯 示例问题

- "请分析人工智能题材"
- "分析新能源汽车板块"
- "半导体行业最新动态"
- "医药生物题材机会"

## 🏗️ 项目结构

```
stock_agent_html/
├── app.py                 # Flask后端应用
├── run.py                 # 启动脚本
├── start.bat             # Windows启动脚本
├── requirements.txt      # Python依赖
├── agent_v3.py          # 核心代理逻辑
├── agent_v3_gradio.py   # 原Gradio版本(已弃用)
├── templates/
│   └── index.html       # HTML模板
├── static/
│   ├── css/
│   │   └── style.css    # 样式文件
│   └── js/
│       └── app.js       # 前端JavaScript
└── config/
    ├── __init__.py
    └── settings.py
```

## 🔧 技术栈

### 后端
- **Flask**: Web框架
- **Flask-SocketIO**: WebSocket支持
- **AgentScope**: 多代理框架
- **Python-dotenv**: 环境变量管理

### 前端
- **HTML5**: 页面结构
- **CSS3**: 样式和动画
- **JavaScript (ES6+)**: 交互逻辑
- **Socket.IO**: 实时通信
- **Marked.js**: Markdown渲染
- **Font Awesome**: 图标库

## 🆚 与Gradio版本的对比

| 特性 | Gradio版本 | HTML版本 |
|------|------------|----------|
| 界面美观度 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 自定义程度 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 响应式设计 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 实时性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 部署灵活性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 开发复杂度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🛠️ 开发说明

### 添加新功能

1. **后端API**: 在 `app.py` 中添加新的路由
2. **前端界面**: 修改 `templates/index.html`
3. **样式**: 在 `static/css/style.css` 中添加样式
4. **交互逻辑**: 在 `static/js/app.js` 中添加JavaScript代码

### 自定义样式

所有样式都在 `static/css/style.css` 中定义，支持：
- CSS变量自定义主题色彩
- 响应式断点调整
- 动画效果自定义

## 🔒 安全说明

- 本系统仅供学习和研究使用
- 投资有风险，决策需谨慎
- 请勿在生产环境中使用默认的SECRET_KEY

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ 完全重写前端界面，弃用Gradio
- ✅ 使用Flask + WebSocket实现实时通信
- ✅ 现代化的响应式UI设计
- ✅ 优化的用户体验和交互流程
- ✅ 更好的错误处理和状态管理

### v1.0.0 (Gradio版本)
- ✅ 基础的多代理系统
- ✅ Gradio界面实现
- ✅ 基本的股票题材分析功能

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅供学习和研究使用。
