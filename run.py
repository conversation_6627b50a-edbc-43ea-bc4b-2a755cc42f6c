#!/usr/bin/env python3
"""
Super Stock Agents启动脚本
使用Flask + WebSocket替代Gradio界面
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import flask_socketio
        import agentscope
        print("✅ 核心依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_environment():
    """检查环境变量"""
    required_vars = ['API_URL', 'API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请在.env文件中设置以下变量:")
        for var in missing_vars:
            print(f"  {var}=your_value_here")
        return False
    
    print("✅ 环境变量检查通过")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        'templates',
        'static/css',
        'static/js',
        'runs',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构检查通过")

def main():
    """主函数"""
    print("🚀 Super Stock Agents启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查环境变量
    if not check_environment():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    print("\n📋 系统信息:")
    print(f"   Python版本: {sys.version}")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   Flask应用: app.py")
    print(f"   访问地址: http://localhost:5000")
    
    print("\n🎯 启动说明:")
    print("   1. 系统将在 http://localhost:5000 启动")
    print("   2. 首先点击'初始化代理'按钮")
    print("   3. 然后在聊天界面输入您想分析的题材")
    print("   4. 系统将协调多个专家为您提供分析")
    
    print("\n👥 专家团队:")
    print("   👔 Morgan (CEO) - 总体规划和任务分配")
    print("   🔍 Leo (信息检索专家) - 网络信息检索和筛选")
    print("   📊 Ken (题材挖掘专家) - 题材逻辑和分支挖掘")
    print("   💹 Lus (A股炒作大师) - 个股挖掘和炒作分析")
    print("   📝 Jess (秘书) - 信息汇总和报告生成")
    
    print("\n" + "=" * 50)
    
    try:
        # 启动Flask应用
        print("🚀 正在启动Flask应用...")
        from app import app, socketio
        socketio.run(
            app, 
            host='0.0.0.0', 
            port=5000, 
            debug=False,
            allow_unsafe_werkzeug=True
        )
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
