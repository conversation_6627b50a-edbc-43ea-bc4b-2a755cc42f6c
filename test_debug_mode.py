#!/usr/bin/env python3
"""
测试debug模式的功能
"""

import os
import sys
import json
import time
from pathlib import Path

# 设置UTF-8编码
if sys.platform.startswith('win'):
    os.system('chcp 65001 >nul 2>&1')
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

def check_debug_mode():
    """检查当前的debug模式设置"""
    
    app_file = "app_file.py"
    
    if not os.path.exists(app_file):
        print(f"❌ 文件不存在: {app_file}")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if "DEBUG_MODE = True" in content:
        print("✅ 当前为测试模式 (DEBUG_MODE = True)")
        return True
    elif "DEBUG_MODE = False" in content:
        print("❌ 当前为正常模式 (DEBUG_MODE = False)")
        print("💡 请运行: python toggle_debug_mode.py 切换到测试模式")
        return False
    else:
        print("❌ 未找到 DEBUG_MODE 设置")
        return False

def check_test_file():
    """检查测试消息文件"""
    
    test_file = Path("messages/session_9c4ab546-48d6-43a1-8a3b-0fef81ed87d6.jsonl")
    
    if not test_file.exists():
        print(f"❌ 测试消息文件不存在: {test_file}")
        return False
    
    # 统计消息数量
    message_count = 0
    system_count = 0
    call_mention_count = 0

    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue

                try:
                    msg = json.loads(line)
                    message_count += 1

                    if msg.get('role') == 'system':
                        system_count += 1

                    content = msg.get('content', '')
                    if '[Call @' in content:
                        call_mention_count += 1

                except json.JSONDecodeError:
                    continue

        print(f"📊 消息统计:")
        print(f"   - 总消息数: {message_count}")
        print(f"   - System消息数: {system_count}")
        print(f"   - 包含Call mention的消息: {call_mention_count}")

        return True

    except Exception as e:
        print(f"❌ 读取测试文件时出错: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    
    try:
        import flask
        import flask_socketio
        print("✅ Flask和Flask-SocketIO已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请运行: pip install flask flask-socketio")
        return False

def main():
    """主函数"""
    
    print("🧪 测试debug模式功能")
    print("=" * 50)
    
    # 检查各项配置
    checks = [
        ("Debug模式设置", check_debug_mode),
        ("测试消息文件", check_test_file),
        ("Python依赖", check_dependencies),
    ]
    
    all_passed = True
    
    for name, check_func in checks:
        print(f"\n🔍 检查 {name}:")
        if not check_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("✅ 所有检查通过！")
        print("\n🚀 启动测试模式:")
        print("   1. 运行: python app_file.py")
        print("   2. 或运行: start_debug.bat")
        print("   3. 打开浏览器访问: http://localhost:5000")
        print("   4. 点击'初始化系统'")
        print("   5. 输入任意消息测试")
        print("\n📝 预期效果:")
        print("   - 系统会加载预设的测试消息")
        print("   - [Call @xxx] 会显示为蓝色的 @xxx")
        print("   - System消息会合并到上一条消息中")
        print("   - 不会启动代理进程")
    else:
        print("❌ 部分检查失败，请修复后重试")
        print("\n💡 修复建议:")
        print("   - 切换到测试模式: python toggle_debug_mode.py")
        print("   - 安装依赖: pip install -r requirements.txt")
        print("   - 确保测试消息文件存在")

if __name__ == "__main__":
    main()
