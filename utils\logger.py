"""
统一的日志管理模块
提供项目级别的日志配置和管理功能
"""

import logging
import os
from datetime import datetime
from pathlib import Path


class Logger:
    """统一的日志管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logger()
            self._initialized = True
    
    def _setup_logger(self):
        """设置日志配置"""
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 获取根日志器
        self.logger = logging.getLogger('stock_agent')
        self.logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(
                log_dir / f"stock_agent_{datetime.now().strftime('%Y%m%d')}.log",
                encoding='utf-8'
            )
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(formatter)
            
            # 控制台处理器（只显示WARNING及以上级别）
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.WARNING)
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def get_logger(self, name=None):
        """获取指定名称的日志器"""
        if name:
            return logging.getLogger(f'stock_agent.{name}')
        return self.logger
    
    def info(self, message):
        """记录信息级别日志"""
        self.logger.info(message)
    
    def warning(self, message):
        """记录警告级别日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """记录错误级别日志"""
        self.logger.error(message)
    
    def debug(self, message):
        """记录调试级别日志"""
        self.logger.debug(message)


# 全局日志器实例
logger = Logger()

# 便捷函数
def get_logger(name=None):
    """获取日志器的便捷函数"""
    return logger.get_logger(name)

def log_info(message):
    """记录信息日志的便捷函数"""
    logger.info(message)

def log_warning(message):
    """记录警告日志的便捷函数"""
    logger.warning(message)

def log_error(message):
    """记录错误日志的便捷函数"""
    logger.error(message)

def log_debug(message):
    """记录调试日志的便捷函数"""
    logger.debug(message)
